#!/bin/bash

echo "🚀 Building Linux Baseline Checker v3.2.0 for Windows (Pure Go Build)..."
echo "📋 This script builds without Fyne packaging for maximum compatibility"

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "❌ Error: Go not found. Please install Go first."
    exit 1
fi

# 检查Go版本
GO_VERSION=$(go version | grep -oE 'go[0-9]+\.[0-9]+' | sed 's/go//')
echo "📋 Go version: $GO_VERSION"

echo "📦 Downloading dependencies..."
go mod tidy

if [ $? -ne 0 ]; then
    echo "❌ Failed to download dependencies!"
    exit 1
fi

# 创建输出目录
mkdir -p dist

echo "🏗️  Building Windows 64-bit version (Pure Go)..."
# Windows 64位版本 - 纯Go构建，最大兼容性
CGO_ENABLED=1 \
GOOS=windows \
GOARCH=amd64 \
CC=x86_64-w64-mingw32-gcc \
CGO_LDFLAGS="-static -static-libgcc -static-libstdc++ -lwinmm -lws2_32 -lole32 -loleaut32 -luuid -lgdi32 -lcomdlg32 -lcomctl32" \
CGO_CFLAGS="-D_WIN32_WINNT=0x0601 -DWINVER=0x0601" \
go build -ldflags="-s -w -H=windowsgui -extldflags=-static" -tags="release" -o "dist/Linux基线检查工具-x64.exe" .

echo "🏗️  Building Windows 32-bit version (Pure Go)..."
# Windows 32位版本 - 纯Go构建
CGO_ENABLED=1 \
GOOS=windows \
GOARCH=386 \
CC=i686-w64-mingw32-gcc \
CGO_LDFLAGS="-static -static-libgcc -static-libstdc++ -lwinmm -lws2_32 -lole32 -loleaut32 -luuid -lgdi32 -lcomdlg32 -lcomctl32" \
CGO_CFLAGS="-D_WIN32_WINNT=0x0601 -DWINVER=0x0601" \
go build -ldflags="-s -w -H=windowsgui -extldflags=-static" -tags="release" -o "dist/Linux基线检查工具-x86.exe" .

echo "⚠️  注意：由于项目使用Fyne GUI框架，无法构建No-CGO版本"
echo "📋 Fyne框架必须使用CGO来调用系统GUI API"
echo "📋 如需最大兼容性，请使用上面的CGO静态链接版本"

# 检查构建结果
echo "🔍 Checking build results..."

BUILD_SUCCESS=false

for exe in dist/*.exe; do
    if [ -f "$exe" ]; then
        echo "✅ Build successful: $(basename "$exe")"
        echo "📊 File size: $(du -h "$exe" | cut -f1)"
        BUILD_SUCCESS=true
    fi
done

if [ "$BUILD_SUCCESS" = true ]; then
    echo ""
    echo "🎯 Pure Go build completed successfully!"
    echo "📁 All executables available in dist/ directory"
    echo ""
    echo "🔧 Version comparison:"
    echo "   📋 CGO versions (Linux基线检查工具-x64/x86.exe):"
    echo "      ✅ 完整的Fyne GUI功能"
    echo "      ✅ 更好的性能和用户体验"
    echo "      ✅ 静态链接，无外部DLL依赖"
    echo "      ✅ 支持所有GUI组件和功能"
    echo "      ⚠️  需要Windows 7+系统支持"
    echo ""
    echo "🚀 部署建议："
    echo "   1. 优先使用64位版本（现代Windows系统）"
    echo "   2. 32位版本适用于老系统或32位Windows"
    echo "   3. 所有版本都是静态链接，无需额外依赖"
    echo ""
    echo "🛡️  兼容性："
    echo "   ✅ Windows 7 SP1及以上版本"
    echo "   ✅ Windows Server 2008 R2及以上版本"
    echo "   ✅ 支持32位和64位架构"
    echo "   ✅ 无外部DLL依赖"
    echo "   ⚠️  不支持Windows XP（Fyne框架限制）"
else
    echo "❌ Pure Go build failed!"
    echo "🔧 Troubleshooting tips:"
    echo "   1. Check Go installation and version"
    echo "   2. Ensure all dependencies are available"
    echo "   3. Check for compilation errors above"
    exit 1
fi
